# StreamBliss 🎬

**StreamBliss** is a modern, secure video and image hosting platform built with cutting-edge technologies. It provides users with fast, reliable, and easy-to-use media hosting services with advanced features like real-time collaboration, community interaction, and subscription-based tiers.

![StreamBliss](https://dev.streambliss.cloud/assets/meta.webp)

## 🌟 Features

### Core Features
- **🎥 Video Hosting** - Upload, process, and stream videos with automatic optimization
- **🖼️ Image Hosting** - Secure image upload and hosting with multiple format support
- **⚡ Real-time Features** - Live notifications, comments, and interactions via WebSockets
- **🔐 Advanced Security** - Two-factor authentication, encrypted storage, and secure access controls
- **💳 Subscription Management** - Multiple tiers (FREE, PRO, CREATOR) with Stripe integration
- **👥 Community Features** - User profiles, comments, likes, subscriptions, and community pages
- **🛡️ Admin Dashboard** - Comprehensive admin panel with user management and analytics
- **📱 Responsive Design** - Mobile-first design with modern UI components

### Advanced Features
- **🎬 Video Processing** - Automatic video optimization, thumbnail generation, and format conversion
- **📊 Analytics** - Detailed usage statistics and monitoring
- **🎫 Support System** - Built-in ticket system for user support
- **🚨 Reporting System** - Content moderation and user reporting
- **🔔 Notifications** - Real-time notification system
- **🌐 Social Integration** - Connect social media accounts and share content
- **🎨 3D Graphics** - Three.js integration for enhanced visual experiences

## 🏗️ Architecture

StreamBliss follows a modern microservices architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (Next.js)     │◄──►│   (NestJS)      │◄──►│   (MySQL)       │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│     Redis       │◄─────────────┘
                        │   (Caching)     │
                        │   Port: 6379    │
                        └─────────────────┘
```

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15.3.1 with React 19.1.0
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom animations
- **UI Components**: Radix UI, Material UI, Lucide React
- **Authentication**: NextAuth.js
- **State Management**: Zustand
- **3D Graphics**: Three.js with React Three Fiber
- **Video Player**: Custom HTML5 player with advanced controls
- **Forms**: React Hook Form with Zod validation
- **Animations**: Framer Motion, GSAP
- **File Upload**: React Dropzone with Multer

### Backend
- **Framework**: NestJS with TypeScript
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT with Passport.js
- **Caching**: Redis with ioredis
- **Queue System**: BullMQ for background jobs
- **File Processing**: FFmpeg for video processing
- **Email**: Nodemailer for email services
- **Payment**: Stripe for subscription management
- **WebSockets**: Socket.io for real-time features
- **Monitoring**: Prometheus metrics
- **Security**: Helmet, rate limiting, encryption

### DevOps & Tools
- **Package Manager**: npm
- **Code Quality**: ESLint, Prettier
- **Database Migrations**: Prisma
- **Environment**: Docker support
- **Monitoring**: Winston logging
- **Testing**: Jest (configured)

## 📋 Prerequisites

Before setting up StreamBliss, ensure you have the following installed:

- **Node.js** >= 20.0.0
- **npm** >= 10.0.0
- **MySQL** >= 8.0
- **Redis** >= 6.0
- **FFmpeg** (for video processing)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/streambliss.git
cd streambliss
```

### 2. Frontend Setup

```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local

# Configure your environment variables (see Environment Variables section)
# Then start the development server
npm run dev
```

The frontend will be available at `http://localhost:3000`

### 3. Backend Setup

```bash
# Navigate to backend directory
cd ../backend/Streambliss-API

# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Configure your environment variables
# Run database migrations
npx prisma migrate dev

# Seed the database (optional)
npx prisma db seed

# Start the development server
npm run start:dev
```

The backend API will be available at `http://localhost:8000`

## ⚙️ Environment Variables

### Frontend (.env.local)

```env
# Database
DATABASE_URL="mysql://username:password@localhost:3306/streambliss"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# API URLs
NEXT_PUBLIC_API_URL="http://localhost:8000"
NEXT_PUBLIC_SOCKET_URL="http://localhost:8000"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
DISCORD_CLIENT_ID="your-discord-client-id"
DISCORD_CLIENT_SECRET="your-discord-client-secret"

# Stripe
STRIPE_SECRET_KEY="sk_test_..."
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# Email
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# File Upload
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE="100MB"

# Security
ENCRYPTION_KEY="your-32-character-encryption-key"
JWT_SECRET="your-jwt-secret"
```

### Backend (.env)

```env
# Database
DATABASE_URL="mysql://username:password@localhost:3306/streambliss_api"

# Redis
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# JWT
JWT_SECRET="your-jwt-secret"
JWT_EXPIRES_IN="7d"

# Encryption
ENCRYPTION_KEY="your-32-character-encryption-key"

# Email
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Stripe
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# File Storage
UPLOAD_PATH="./uploads"
MAX_FILE_SIZE=104857600

# API Configuration
PORT=8000
NODE_ENV="development"

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Monitoring
PROMETHEUS_PORT=9090
```

## 🗄️ Database Setup

### 1. Create Database

```sql
CREATE DATABASE streambliss;
CREATE DATABASE streambliss_api;
```

### 2. Run Migrations

```bash
# Frontend database
cd streambliss
npx prisma migrate dev

# Backend database
cd ../backend/Streambliss-API
npx prisma migrate dev
```

### 3. Seed Database (Optional)

```bash
# Create initial roles and permissions
npx prisma db seed
```

## 📁 Project Structure

### Frontend Structure
```
streambliss/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── (auth)/            # Authentication pages
│   │   ├── (legal)/           # Legal pages
│   │   ├── admin/             # Admin dashboard
│   │   ├── api/               # API routes
│   │   ├── community/         # Community pages
│   │   ├── dashboard/         # User dashboard
│   │   └── v/                 # Video pages
│   ├── components/            # React components
│   │   ├── auth/              # Authentication components
│   │   ├── common/            # Shared components
│   │   ├── ui/                # UI components
│   │   └── video/             # Video-related components
│   ├── hooks/                 # Custom React hooks
│   ├── lib/                   # Utility libraries
│   ├── server/                # Server-side functions
│   └── types/                 # TypeScript type definitions
├── prisma/                    # Database schema and migrations
├── public/                    # Static assets
└── utils/                     # Utility functions
```

### Backend Structure
```
backend/Streambliss-API/
├── src/
│   ├── admin/                 # Admin module
│   ├── auth/                  # Authentication module
│   ├── common/                # Shared utilities
│   ├── coupons/               # Coupon management
│   ├── email-verification/    # Email verification
│   ├── media/                 # Media processing
│   │   ├── image/             # Image handling
│   │   └── video/             # Video processing
│   ├── monitoring/            # System monitoring
│   ├── password-reset/        # Password reset
│   ├── queue/                 # Background jobs
│   ├── subscription/          # Stripe subscriptions
│   ├── tickets/               # Support tickets
│   ├── two-factor/            # 2FA implementation
│   ├── users/                 # User management
│   ├── websocket/             # WebSocket handling
│   └── types/                 # Type definitions
└── prisma/                    # Database schema

## 🔧 Development

### Available Scripts

#### Frontend
```bash
npm run dev          # Start development server with Turbopack
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run clean        # Clean build artifacts
```

#### Backend
```bash
npm run start:dev    # Start development server with hot reload
npm run start:debug  # Start with debugging enabled
npm run build        # Build for production
npm run start:prod   # Start production server
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run lint         # Run ESLint and fix issues
```

### Database Operations

```bash
# Generate Prisma client
npx prisma generate

# View database in Prisma Studio
npx prisma studio

# Reset database
npx prisma migrate reset

# Deploy migrations to production
npx prisma migrate deploy
```

## 📚 API Documentation

The backend API provides comprehensive endpoints for all platform features:

### Authentication Endpoints
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/verify-email` - Email verification
- `POST /auth/forgot-password` - Password reset request

### User Management
- `GET /users/profile` - Get user profile
- `PUT /users/profile` - Update user profile
- `GET /users/settings` - Get user settings
- `PUT /users/settings` - Update user settings
- `POST /users/avatar` - Upload avatar

### Media Endpoints
- `POST /media/video/upload` - Upload video
- `GET /media/video/:id` - Get video details
- `DELETE /media/video/:id` - Delete video
- `POST /media/image/upload` - Upload image
- `GET /media/image/:id` - Get image

### Subscription Management
- `GET /subscription/plans` - Get available plans
- `POST /subscription/create` - Create subscription
- `POST /subscription/cancel` - Cancel subscription
- `GET /subscription/status` - Get subscription status

### Admin Endpoints
- `GET /admin/users` - List all users
- `GET /admin/videos` - List all videos
- `POST /admin/ban-user` - Ban user
- `GET /admin/reports` - Get reports
- `PUT /admin/reports/:id` - Update report status

For detailed API documentation, visit `http://localhost:8000/api/docs` when running the backend.

## 🎯 Subscription Tiers

### FREE Tier
- ✅ Basic video upload (up to 100MB)
- ✅ Basic image upload (up to 10MB)
- ✅ Standard video quality
- ✅ Community features
- ✅ Basic support

### PRO Tier ($9.99/month)
- ✅ All FREE features
- ✅ Enhanced video upload (up to 500MB)
- ✅ Enhanced image upload (up to 50MB)
- ✅ HD video quality
- ✅ Priority support
- ✅ Advanced analytics
- ✅ Custom branding

### CREATOR Tier ($19.99/month)
- ✅ All PRO features
- ✅ Unlimited video upload (up to 2GB)
- ✅ Unlimited image upload (up to 100MB)
- ✅ 4K video quality
- ✅ Premium support
- ✅ Advanced customization
- ✅ API access
- ✅ White-label options

## 🔐 Security Features

- **🔒 Two-Factor Authentication** - TOTP-based 2FA with QR code setup
- **🛡️ JWT Authentication** - Secure token-based authentication
- **🔐 Data Encryption** - Sensitive data encrypted at rest
- **🚫 Rate Limiting** - API rate limiting to prevent abuse
- **🔍 Input Validation** - Comprehensive input validation and sanitization
- **🛡️ CORS Protection** - Proper CORS configuration
- **🔒 Helmet Security** - Security headers with Helmet.js
- **📝 Audit Logging** - Comprehensive audit trail for all actions

## 🚀 Deployment

### Production Environment Variables

Ensure all environment variables are properly configured for production:

```bash
# Set NODE_ENV to production
NODE_ENV=production

# Use production database URLs
DATABASE_URL="mysql://user:pass@prod-db:3306/streambliss"

# Use production Redis
REDIS_HOST="prod-redis-host"

# Use production Stripe keys
STRIPE_SECRET_KEY="sk_live_..."

# Configure production email
EMAIL_SERVER_HOST="your-production-smtp"
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build individual containers
docker build -t streambliss-frontend .
docker build -t streambliss-backend ./backend/Streambliss-API
```

### Production Checklist

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates installed
- [ ] CDN configured for static assets
- [ ] Monitoring and logging set up
- [ ] Backup strategy implemented
- [ ] Security headers configured
- [ ] Rate limiting enabled

## 🤝 Contributing

We welcome contributions to StreamBliss! Please follow these guidelines:

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make your changes** and ensure they follow our coding standards
4. **Add tests** for new functionality
5. **Run the test suite**: `npm test`
6. **Commit your changes**: `git commit -m 'Add amazing feature'`
7. **Push to the branch**: `git push origin feature/amazing-feature`
8. **Open a Pull Request**

### Code Style

- Use TypeScript for all new code
- Follow ESLint and Prettier configurations
- Write meaningful commit messages
- Add JSDoc comments for functions
- Ensure responsive design for UI components

### Testing

- Write unit tests for new functions
- Add integration tests for API endpoints
- Test UI components with user interactions
- Ensure cross-browser compatibility

## 📝 TODO & Roadmap

### Current Development Tasks
- [ ] Package limits implementation (Upload size restrictions)
- [ ] Legal pages (Impressum, Datenschutz)
- [ ] Production console log cleanup
- [ ] Community page post-upload prompt
- [ ] Responsive design fixes
- [ ] Email template redesign
- [ ] Community and Profile page completion
- [ ] Social links in Dashboard Profile
- [ ] Admin-only profile viewing permissions
- [ ] Image shortlink fixes
- [ ] Google OAuth integration

### Future Features
- [ ] Mobile app development
- [ ] Advanced video analytics
- [ ] Live streaming capabilities
- [ ] AI-powered content moderation
- [ ] Multi-language support
- [ ] Advanced video editing tools
- [ ] Collaboration features
- [ ] API rate limiting per tier
- [ ] Advanced search and filtering
- [ ] Content recommendation engine

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Next.js** team for the amazing React framework
- **NestJS** team for the powerful Node.js framework
- **Prisma** team for the excellent ORM
- **Stripe** for payment processing
- **All contributors** who help make StreamBliss better

## 📞 Support

- **Documentation**: [docs.streambliss.cloud](https://docs.streambliss.cloud)
- **Issues**: [GitHub Issues](https://github.com/yourusername/streambliss/issues)
- **Discussions**: [GitHub Discussions](https://github.com/yourusername/streambliss/discussions)
- **Email**: <EMAIL>

---

**Made with ❤️ by the StreamBliss Team**
```
